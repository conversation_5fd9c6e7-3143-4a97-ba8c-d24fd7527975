import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/middleware';
import { supabaseAdmin } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  return withAuth(request, async (req) => {
    try {
      const { searchParams } = new URL(request.url);
      const startDate = searchParams.get('start_date');
      const endDate = searchParams.get('end_date');

      // Validate required parameters
      if (!startDate || !endDate) {
        return NextResponse.json({
          success: false,
          error: 'start_date and end_date parameters are required',
          data: null
        }, { status: 400 });
      }

      // Validate date format and range
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return NextResponse.json({
          success: false,
          error: 'Invalid date format. Use YYYY-MM-DD format',
          data: null
        }, { status: 400 });
      }

      if (start > end) {
        return NextResponse.json({
          success: false,
          error: 'start_date cannot be after end_date',
          data: null
        }, { status: 400 });
      }

      // Get all active buses with their capacity
      const { data: busData, error: busError } = await supabaseAdmin
        .from('buses')
        .select('route_code, name, total_seats, is_active')
        .eq('is_active', true)
        .order('name');

      if (busError) {
        console.error('Error fetching bus data:', busError);
        return NextResponse.json({
          success: false,
          error: 'Failed to fetch bus data',
          data: null
        }, { status: 500 });
      }

      // Get bookings data within date range
      const { data: bookingsData, error: bookingsError } = await supabaseAdmin
        .from('bookings')
        .select('bus_route, created_at')
        .gte('created_at', startDate + 'T00:00:00.000Z')
        .lte('created_at', endDate + 'T23:59:59.999Z');

      if (bookingsError) {
        console.error('Error fetching bookings for route demand:', bookingsError);
        return NextResponse.json({
          success: false,
          error: 'Failed to fetch bookings data',
          data: null
        }, { status: 500 });
      }

      // Count bookings per route
      const routeBookingCounts: { [key: string]: number } = {};
      bookingsData?.forEach((booking: any) => {
        const route = booking.bus_route;
        routeBookingCounts[route] = (routeBookingCounts[route] || 0) + 1;
      });

      // Calculate demand metrics for each route
      const routes = busData?.map((bus: any) => {
        const bookingCount = routeBookingCounts[bus.route_code] || 0;
        const capacity = bus.total_seats;
        const utilizationPercentage = capacity > 0 ? (bookingCount / capacity) * 100 : 0;
        
        // Classify demand level based on utilization percentage
        let demandLevel: 'High' | 'Medium' | 'Low';
        if (utilizationPercentage >= 80) {
          demandLevel = 'High';
        } else if (utilizationPercentage >= 50) {
          demandLevel = 'Medium';
        } else {
          demandLevel = 'Low';
        }

        return {
          routeCode: bus.route_code,
          busName: bus.name,
          totalBookings: bookingCount,
          capacity: capacity,
          utilizationPercentage: Math.round(utilizationPercentage * 100) / 100, // Round to 2 decimal places
          demandLevel,
          availableSeats: Math.max(0, capacity - bookingCount),
          isActive: bus.is_active
        };
      }) || [];

      // Sort by booking count (highest demand first)
      routes.sort((a, b) => b.totalBookings - a.totalBookings);

      // Calculate daily demand trends
      const dailyBookings: { [key: string]: { [route: string]: number } } = {};
      bookingsData?.forEach((booking: any) => {
        const date = booking.created_at.split('T')[0];
        const route = booking.bus_route;
        
        if (!dailyBookings[date]) {
          dailyBookings[date] = {};
        }
        dailyBookings[date][route] = (dailyBookings[date][route] || 0) + 1;
      });

      const dailyTrends = Object.entries(dailyBookings)
        .map(([date, routeBookings]) => ({
          date,
          totalBookings: Object.values(routeBookings).reduce((sum: number, count: number) => sum + count, 0),
          routeBreakdown: routeBookings
        }))
        .sort((a, b) => a.date.localeCompare(b.date));

      // Calculate summary statistics
      const totalBookings = routes.reduce((sum, route) => sum + route.totalBookings, 0);
      const totalCapacity = routes.reduce((sum, route) => sum + route.capacity, 0);
      const overallUtilization = totalCapacity > 0 ? (totalBookings / totalCapacity) * 100 : 0;

      const demandDistribution = {
        high: routes.filter(r => r.demandLevel === 'High').length,
        medium: routes.filter(r => r.demandLevel === 'Medium').length,
        low: routes.filter(r => r.demandLevel === 'Low').length
      };

      const response = {
        success: true,
        data: {
          routes,
          dailyTrends,
          summary: {
            dateRange: {
              startDate,
              endDate
            },
            totalRoutes: routes.length,
            totalBookings,
            totalCapacity,
            overallUtilization: Math.round(overallUtilization * 100) / 100,
            demandDistribution,
            highestDemandRoute: routes.length > 0 ? routes[0] : null,
            lowestDemandRoute: routes.length > 0 ? routes[routes.length - 1] : null
          }
        }
      };

      return NextResponse.json(response, {
        headers: {
          'Cache-Control': 'max-age=60', // 1-minute cache for custom reports
          'Content-Type': 'application/json'
        }
      });

    } catch (error) {
      console.error('Unexpected error in custom route demand endpoint:', error);
      return NextResponse.json({
        success: false,
        error: 'Internal server error',
        data: null
      }, { status: 500 });
    }
  });
}
