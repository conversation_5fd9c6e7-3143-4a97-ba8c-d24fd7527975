import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/middleware';
import { supabaseAdmin } from '@/lib/supabase';

interface Booking {
  id: number;
  admission_number: string;
  student_name: string;
  bus_route: string;
  destination: string;
  payment_status: boolean;
  fare: number | null;
  created_at: string;
}

// Utility function to validate route values
function isValidRoute(route: any): boolean {
  return route && 
         typeof route === 'string' && 
         route.trim() !== '' && 
         route !== 'undefined' && 
         route !== 'null' && 
         route !== 'None' &&
         route.trim().length > 0;
}

export async function GET(request: NextRequest) {
  return withAuth(request, async (req) => {
    try {
      const { searchParams } = new URL(request.url);
      const startDate = searchParams.get('start_date');
      const endDate = searchParams.get('end_date');
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '50');
      const busRoute = searchParams.get('bus_route');
      const paymentStatus = searchParams.get('payment_status');

      // Validate required parameters
      if (!startDate || !endDate) {
        return NextResponse.json({
          success: false,
          error: 'start_date and end_date parameters are required',
          data: null
        }, { status: 400 });
      }

      // Validate date format and range
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return NextResponse.json({
          success: false,
          error: 'Invalid date format. Use YYYY-MM-DD format',
          data: null
        }, { status: 400 });
      }

      if (start > end) {
        return NextResponse.json({
          success: false,
          error: 'start_date cannot be after end_date',
          data: null
        }, { status: 400 });
      }

      const offset = (page - 1) * limit;

      // Build query with date range filtering - using created_at date part only
      let query = supabaseAdmin
        .from('bookings')
        .select(`
          id,
          admission_number,
          student_name,
          bus_route,
          destination,
          payment_status,
          fare,
          created_at
        `, { count: 'exact' })
        .gte('created_at', startDate)
        .lte('created_at', endDate)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply additional filters
      if (busRoute && busRoute !== 'all' && isValidRoute(busRoute)) {
        query = query.eq('bus_route', busRoute);
      }

      if (paymentStatus && paymentStatus !== 'all') {
        query = query.eq('payment_status', paymentStatus === 'true');
      }

      console.log('Executing query with params:', { startDate, endDate, busRoute, paymentStatus });

      const { data: bookings, error, count } = await query;

      if (error) {
        console.error('Supabase error fetching bookings:', error);
        return NextResponse.json({
          success: false,
          error: `Database error: ${error.message}`,
          data: null
        }, { status: 500 });
      }

      console.log(`Found ${count || 0} total bookings, returning ${bookings?.length || 0} records`);

      // Calculate summary statistics safely
      const totalBookings = count || 0;
      const bookingsList = (bookings as Booking[]) || [];
      
      // Log route validation for debugging
      const allRoutes = bookingsList.map(b => b.bus_route);
      const invalidRoutes = allRoutes.filter(route => !isValidRoute(route));
      if (invalidRoutes.length > 0) {
        console.log('Found invalid routes that will be filtered out:', invalidRoutes);
      }
      
      const paidBookings = bookingsList.filter(b => b.payment_status === true).length;
      const unpaidBookings = totalBookings - paidBookings;

      const totalRevenue = bookingsList.reduce((sum: number, booking: Booking) => {
        return sum + (booking.payment_status === true ? (booking.fare || 0) : 0);
      }, 0);

      // Get unique routes for filtering - ensure no empty values
      const uniqueRoutes: string[] = Array.from(
        new Set(
          bookingsList
            .map(b => b.bus_route)
            .filter(isValidRoute)
        )
      ).sort();

      // Final validation to ensure no empty strings slip through
      const cleanAvailableRoutes = uniqueRoutes.filter(route => 
        route && 
        typeof route === 'string' && 
        route.trim() !== '' && 
        route.length > 0
      );

      console.log('Available routes after validation:', cleanAvailableRoutes);

      const response = {
        success: true,
        data: {
          bookings: bookingsList.filter(booking => isValidRoute(booking.bus_route)),
          pagination: {
            page,
            limit,
            total: totalBookings,
            totalPages: Math.ceil(totalBookings / limit)
          },
          summary: {
            totalBookings,
            paidBookings,
            unpaidBookings,
            totalRevenue,
            uniqueRoutes: cleanAvailableRoutes.length
          },
          filters: {
            dateRange: {
              startDate,
              endDate
            },
            busRoute: busRoute || null,
            paymentStatus: paymentStatus || null
          },
          availableRoutes: cleanAvailableRoutes
        }
      };

      return NextResponse.json(response, {
        headers: {
          'Cache-Control': 'max-age=60',
          'Content-Type': 'application/json'
        }
      });

    } catch (error) {
      console.error('Unexpected error in custom bookings endpoint:', error);
      return NextResponse.json({
        success: false,
        error: `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: null
      }, { status: 500 });
    }
  });
}
